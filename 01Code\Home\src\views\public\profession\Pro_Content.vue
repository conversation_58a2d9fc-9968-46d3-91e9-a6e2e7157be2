<template>
  <div class="content-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1>专业内容</h1>
      <div class="page-header-desc">
        <div class="data">
          <h1>68</h1>
          <p>专业课程</p>
        </div>
        <div class="data">
          <h1>5</h1>
          <p>培养方案</p>
        </div>
        <div class="data">
          <h1>12</h1>
          <p>实践基地</p>
        </div>
      </div>
    </div>

    <!-- 主内容区 -->
    <div class="main-content">
      <!-- 左侧导航标签 -->
      <div class="nav-container">
        <button
          v-for="tab in tabs"
          :key="tab.title"
          @click="switchTab(tab)"
          :class="{ active: activeTab.title === tab.title }"
        >
          {{ tab.title }}
        </button>
      </div>

      <!-- 右侧内容区 -->
      <div class="content">
        <div v-if="activeTab.title === '专业内容'">
          <!-- 加载状态 -->
          <div v-if="isLoading" class="loading-container">
            <div class="loading-spinner"></div>
            <p>正在加载专业内容...</p>
          </div>

          <!-- 错误状态 -->
          <div v-else-if="error" class="error-container">
            <div class="error-icon">⚠️</div>
            <p class="error-message">{{ error }}</p>
            <button @click="fetchMajorContent" class="retry-btn">重试</button>
          </div>

          <!-- 专业内容数据 -->
          <div v-else class="major-content">
            <!-- 专业介绍 -->
            <div v-if="majorContentData.introduce" class="content-section">
              <div v-html="majorContentData.introduce" class="html-content"></div>
            </div>

            <!-- 专业信息 -->
            <div v-if="majorContentData.info" class="content-section">
              <h3>专业信息</h3>
              <div v-html="majorContentData.info" class="html-content"></div>
            </div>

            <!-- 其他字段（如果有数据的话） -->
            <div v-if="majorContentData.target" class="content-section">
              <h3>培养目标</h3>
              <div v-html="majorContentData.target" class="html-content"></div>
            </div>

            <div v-if="majorContentData.coursePlan" class="content-section">
              <h3>课程计划</h3>
              <div v-html="majorContentData.coursePlan" class="html-content"></div>
            </div>

            <div v-if="majorContentData.course" class="content-section">
              <h3>课程设置</h3>
              <div v-html="majorContentData.course" class="html-content"></div>
            </div>

            <div v-if="majorContentData.relation" class="content-section">
              <h3>相关信息</h3>
              <div v-html="majorContentData.relation" class="html-content"></div>
            </div>

            <div v-if="majorContentData.requirement" class="content-section">
              <h3>专业要求</h3>
              <div v-html="majorContentData.requirement" class="html-content"></div>
            </div>

            <div v-if="majorContentData.teachingPlan" class="content-section">
              <h3>教学计划</h3>
              <div v-html="majorContentData.teachingPlan" class="html-content"></div>
            </div>

            <div v-if="majorContentData.cultivationPlan" class="content-section">
              <h3>培养方案</h3>
              <div v-html="majorContentData.cultivationPlan" class="html-content"></div>
            </div>

            <!-- 如果没有任何内容 -->
            <div v-if="!hasAnyContent" class="empty-content">
              <p>暂无专业内容数据</p>
            </div>
          </div>
        </div>
        <div v-if="activeTab.title === '专业调研'">
          <!-- 加载状态 -->
          <div v-if="isLoading" class="loading-container">
            <div class="loading-spinner"></div>
            <p>正在加载专业调研...</p>
          </div>

          <!-- 错误状态 -->
          <div v-else-if="error" class="error-container">
            <div class="error-icon">⚠️</div>
            <p class="error-message">{{ error }}</p>
            <button @click="fetchMajorInvestigation" class="retry-btn">重试</button>
          </div>

          <!-- 专业调研数据 -->
          <div v-else class="major-investigation">
            <div v-if="majorInvestigationData.content" class="content-section">
              <h3>调研内容</h3>
              <div class="investigation-content">
                <p>{{ majorInvestigationData.content }}</p>
              </div>
            </div>

            <!-- 调研信息 -->
            <div v-if="majorInvestigationData.createUserName || majorInvestigationData.updateUserName" class="meta-info">
              <h3>调研信息</h3>
              <div class="meta-details">
                <p v-if="majorInvestigationData.createUserName">
                  <strong>创建者：</strong>{{ majorInvestigationData.createUserName }}
                </p>
                <p v-if="majorInvestigationData.createTime">
                  <strong>创建时间：</strong>{{ formatTime(majorInvestigationData.createTime) }}
                </p>
                <p v-if="majorInvestigationData.updateUserName">
                  <strong>更新者：</strong>{{ majorInvestigationData.updateUserName }}
                </p>
                <p v-if="majorInvestigationData.updateTime">
                  <strong>更新时间：</strong>{{ formatTime(majorInvestigationData.updateTime) }}
                </p>
              </div>
            </div>

            <!-- 如果没有内容 -->
            <div v-if="!majorInvestigationData.content" class="empty-content">
              <p>暂无专业调研数据</p>
            </div>
          </div>
        </div>
        <div v-if="activeTab.title === '人才培养方案'">
          <!-- 加载状态 -->
          <div v-if="isLoading" class="loading-container">
            <div class="loading-spinner"></div>
            <p>正在加载人才培养方案...</p>
          </div>

          <!-- 错误状态 -->
          <div v-else-if="error" class="error-container">
            <div class="error-icon">⚠️</div>
            <p class="error-message">{{ error }}</p>
            <button @click="fetchMajorCultivationList" class="retry-btn">重试</button>
          </div>

          <!-- 人才培养方案列表数据 -->
          <div v-else class="major-cultivation">
            <div v-if="majorCultivationData.records && majorCultivationData.records.length > 0" class="cultivation-list">
              <h3>人才培养方案列表</h3>
              <div class="list-container">
                <div
                  v-for="item in majorCultivationData.records"
                  :key="item.cultivationId"
                  class="cultivation-item"
                >
                  <div class="item-header">
                    <h4 class="item-title">{{ item.name || '未命名方案' }}</h4>
                    <span class="item-year" v-if="item.year">{{ item.year }}年</span>
                  </div>

                  <div class="item-content">
                    <div class="file-info">
                      <p><strong>文件名：</strong>{{ item.fileName || '未知' }}</p>
                      <p><strong>文件类型：</strong>{{ item.fileType || '未知' }}</p>
                      <p><strong>文件大小：</strong>{{ formatFileSize(item.fileSize) }}</p>
                    </div>

                    <div class="meta-info">
                      <p v-if="item.createUserName">
                        <strong>创建者：</strong>{{ item.createUserName }}
                      </p>
                      <p v-if="item.createTime">
                        <strong>创建时间：</strong>{{ formatTime(item.createTime) }}
                      </p>
                      <p v-if="item.updateUserName">
                        <strong>更新者：</strong>{{ item.updateUserName }}
                      </p>
                      <p v-if="item.updateTime">
                        <strong>更新时间：</strong>{{ formatTime(item.updateTime) }}
                      </p>
                    </div>
                  </div>

                  <div class="item-actions" v-if="item.filePath">
                    <a :href="item.filePath" target="_blank" class="download-btn">
                      <i class="fas fa-download"></i>
                      下载文件
                    </a>
                  </div>
                </div>
              </div>

              <!-- 分页信息 -->
              <div v-if="majorCultivationData.total > 0" class="pagination-info">
                <p>
                  共 {{ majorCultivationData.total }} 条记录，
                  第 {{ majorCultivationData.current }} / {{ majorCultivationData.pages }} 页
                </p>
              </div>
            </div>

            <!-- 如果没有数据 -->
            <div v-else class="empty-content">
              <p>暂无人才培养方案数据</p>
            </div>
          </div>
        </div>
        <div v-if="activeTab.title === 'AI岗位分析'">
          <!-- 加载状态 -->
          <div v-if="isLoading" class="loading-container">
            <div class="loading-spinner"></div>
            <p>正在加载AI岗位分析...</p>
          </div>

          <!-- 错误状态 -->
          <div v-else-if="error" class="error-container">
            <div class="error-icon">⚠️</div>
            <p class="error-message">{{ error }}</p>
            <button @click="fetchMajorAiJob" class="retry-btn">重试</button>
          </div>

          <!-- AI岗位分析数据 -->
          <div v-else class="major-ai-job">
            <div v-if="majorAiJobData.content" class="content-section">
              <h3>AI岗位分析内容</h3>
              <div class="ai-job-content">
                <p>{{ majorAiJobData.content }}</p>
              </div>
            </div>

            <!-- 分析信息 -->
            <div v-if="majorAiJobData.createUserName || majorAiJobData.updateUserName" class="meta-info">
              <h3>分析信息</h3>
              <div class="meta-details">
                <p v-if="majorAiJobData.createUserName">
                  <strong>创建者：</strong>{{ majorAiJobData.createUserName }}
                </p>
                <p v-if="majorAiJobData.createTime">
                  <strong>创建时间：</strong>{{ formatTime(majorAiJobData.createTime) }}
                </p>
                <p v-if="majorAiJobData.updateUserName">
                  <strong>更新者：</strong>{{ majorAiJobData.updateUserName }}
                </p>
                <p v-if="majorAiJobData.updateTime">
                  <strong>更新时间：</strong>{{ formatTime(majorAiJobData.updateTime) }}
                </p>
              </div>
            </div>

            <!-- 如果没有内容 -->
            <div v-if="!majorAiJobData.content" class="empty-content">
              <p>暂无AI岗位分析数据</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue';
import { getMajorContent, getMajorInvestigation, getMajorAiJob, getMajorCultivationList } from '@/api/public/profession/majorContent';

const tabs = [
  { title: '专业内容' },
  { title: '专业调研' },
  { title: '人才培养方案' },
  { title: 'AI岗位分析' },
];
const activeTab = ref(tabs[0]);

// 专业内容数据
const majorContentData = ref({
  contentId: 'content_001',
  introduce: `
    <h2>计算机科学与技术专业介绍</h2>
    <p>计算机科学与技术专业是一门综合性的学科，涵盖了计算机硬件、软件、网络、数据库、人工智能等多个领域。本专业致力于培养具有扎实的计算机科学理论基础和较强的工程实践能力的高级专门人才。</p>
    <p>专业特色：</p>
    <ul>
      <li>理论与实践并重，注重学生动手能力的培养</li>
      <li>紧跟技术发展趋势，课程设置与时俱进</li>
      <li>产学研结合，与知名企业建立合作关系</li>
      <li>国际化视野，引进先进的教学理念和方法</li>
    </ul>
  `,
  info: `
    <h3>专业基本信息</h3>
    <p><strong>专业代码：</strong>080901</p>
    <p><strong>学制：</strong>四年</p>
    <p><strong>学位：</strong>工学学士</p>
    <p><strong>所属学院：</strong>计算机科学与工程学院</p>
    <p><strong>专业负责人：</strong>张教授</p>
    <p><strong>联系电话：</strong>010-12345678</p>
    <p><strong>专业网站：</strong>http://cs.university.edu.cn</p>
  `,
  target: `
    <h3>人才培养目标</h3>
    <p>本专业培养德、智、体、美、劳全面发展，具有良好的科学素养和人文素养，系统掌握计算机科学与技术的基本理论、基本知识和基本技能，具备较强的工程实践能力和创新意识的高级专门人才。</p>
    <p>具体目标：</p>
    <ol>
      <li>掌握扎实的数学、自然科学基础知识</li>
      <li>系统掌握计算机科学与技术的核心理论和方法</li>
      <li>具备软件设计与开发的工程实践能力</li>
      <li>具有良好的团队合作精神和沟通能力</li>
      <li>具备终身学习的意识和能力</li>
    </ol>
  `,
  coursePlan: `
    <h3>课程规划</h3>
    <p>本专业课程体系分为通识教育课程、学科基础课程、专业核心课程和专业选修课程四个模块。</p>
    <h4>学分分配：</h4>
    <ul>
      <li>通识教育课程：48学分</li>
      <li>学科基础课程：42学分</li>
      <li>专业核心课程：36学分</li>
      <li>专业选修课程：24学分</li>
      <li>实践环节：20学分</li>
      <li>总计：170学分</li>
    </ul>
  `,
  course: `
    <h3>主要课程设置</h3>
    <h4>学科基础课程：</h4>
    <ul>
      <li>高等数学、线性代数、概率论与数理统计</li>
      <li>离散数学、数字逻辑</li>
      <li>程序设计基础（C语言）</li>
      <li>数据结构与算法</li>
    </ul>
    <h4>专业核心课程：</h4>
    <ul>
      <li>计算机组成原理</li>
      <li>操作系统</li>
      <li>计算机网络</li>
      <li>数据库系统原理</li>
      <li>软件工程</li>
      <li>编译原理</li>
    </ul>
    <h4>专业选修课程：</h4>
    <ul>
      <li>人工智能、机器学习</li>
      <li>Web开发技术</li>
      <li>移动应用开发</li>
      <li>信息安全</li>
      <li>大数据技术</li>
    </ul>
  `,
  relation: `
    <h3>相关信息</h3>
    <h4>就业方向：</h4>
    <ul>
      <li>软件开发工程师</li>
      <li>系统架构师</li>
      <li>数据库管理员</li>
      <li>网络工程师</li>
      <li>产品经理</li>
      <li>技术顾问</li>
    </ul>
    <h4>深造方向：</h4>
    <ul>
      <li>计算机科学与技术硕士</li>
      <li>软件工程硕士</li>
      <li>人工智能硕士</li>
      <li>网络空间安全硕士</li>
    </ul>
  `,
  requirement: `
    <h3>专业要求</h3>
    <h4>入学要求：</h4>
    <ul>
      <li>具有良好的数学基础</li>
      <li>对计算机技术有浓厚兴趣</li>
      <li>具备一定的逻辑思维能力</li>
      <li>英语水平达到要求</li>
    </ul>
    <h4>毕业要求：</h4>
    <ul>
      <li>完成所有必修课程学习</li>
      <li>修满170学分</li>
      <li>通过毕业设计答辩</li>
      <li>英语四级成绩合格</li>
      <li>计算机等级考试二级合格</li>
    </ul>
  `,
  teachingPlan: `
    <h3>教学计划</h3>
    <h4>第一学年：</h4>
    <p>重点学习基础理论课程，包括高等数学、程序设计基础等，建立扎实的理论基础。</p>
    <h4>第二学年：</h4>
    <p>深入学习专业基础课程，如数据结构、计算机组成原理等，培养专业思维。</p>
    <h4>第三学年：</h4>
    <p>学习专业核心课程，参与实践项目，提升工程实践能力。</p>
    <h4>第四学年：</h4>
    <p>完成专业选修课程学习，进行毕业设计，准备就业或深造。</p>
  `,
  cultivationPlan: `
    <h3>人才培养方案</h3>
    <p>本专业采用"理论+实践+创新"的培养模式，注重学生综合素质的培养。</p>
    <h4>培养特色：</h4>
    <ul>
      <li>校企合作：与知名IT企业建立实习基地</li>
      <li>项目驱动：以实际项目为载体进行教学</li>
      <li>国际交流：与国外高校建立交换生项目</li>
      <li>创新创业：鼓励学生参与科研和创业活动</li>
    </ul>
    <h4>实践环节：</h4>
    <ul>
      <li>课程设计：每学期安排相应的课程设计</li>
      <li>专业实习：第三学年安排企业实习</li>
      <li>毕业设计：第四学年完成毕业设计项目</li>
      <li>竞赛活动：鼓励参加各类程序设计竞赛</li>
    </ul>
  `,
  createTime: Date.now() - 86400000 * 30, // 30天前
  updateTime: Date.now() - 86400000 * 7   // 7天前
});

// 专业调研数据
const majorInvestigationData = ref({
  investigationId: 'investigation_001',
  content: `
    计算机科学与技术专业市场调研报告

    一、行业发展现状
    随着数字化转型的深入推进，计算机科学与技术专业人才需求持续增长。根据最新的行业调研数据显示，未来5年内，相关岗位需求将增长35%以上。

    二、人才需求分析
    1. 软件开发工程师：市场需求量最大，占比约40%
    2. 数据分析师：新兴热门岗位，需求增长迅速
    3. 人工智能工程师：高薪岗位，要求较高
    4. 网络安全工程师：随着网络安全重要性提升，需求稳定增长
    5. 产品经理：技术背景的产品经理更受欢迎

    三、薪资水平调研
    - 应届毕业生：8-15万/年
    - 工作3-5年：15-30万/年
    - 高级工程师：30-50万/年
    - 技术专家：50万+/年

    四、技能要求趋势
    1. 编程语言：Java、Python、JavaScript需求最高
    2. 框架技术：Spring、React、Vue等主流框架
    3. 数据库：MySQL、Redis、MongoDB等
    4. 云计算：AWS、阿里云等云平台技术
    5. 新兴技术：人工智能、区块链、物联网

    五、就业地区分布
    一线城市（北上广深）仍是主要就业地区，占比60%；新一线城市（杭州、成都、武汉等）发展迅速，占比25%；其他城市占比15%。

    六、企业类型分析
    1. 互联网公司：薪资高，发展快，竞争激烈
    2. 传统IT企业：稳定性好，技术积累深厚
    3. 金融科技：薪资优厚，对技术要求高
    4. 创业公司：机会多，风险与收益并存
    5. 国企央企：稳定性最好，福利待遇完善

    七、专业建设建议
    1. 加强实践教学，提升学生动手能力
    2. 紧跟技术发展趋势，及时更新课程内容
    3. 加强校企合作，为学生提供更多实习机会
    4. 注重软技能培养，提升学生综合素质
    5. 建立校友网络，为学生就业提供更多资源
  `,
  createUserId: 'user_001',
  createTime: Date.now() - 86400000 * 15, // 15天前
  updateUserId: 'user_002',
  updateTime: Date.now() - 86400000 * 3,  // 3天前
  createUserName: '李教授',
  updateUserName: '王副教授'
});

// AI岗位分析数据
const majorAiJobData = ref({
  jobAnalysisId: 'ai_job_001',
  content: `
    人工智能相关岗位分析报告

    一、AI岗位概述
    随着人工智能技术的快速发展，AI相关岗位已成为计算机专业毕业生的热门选择。这些岗位不仅薪资优厚，而且具有广阔的发展前景。

    二、主要AI岗位类型

    1. 机器学习工程师
    - 职责：设计和实现机器学习算法，优化模型性能
    - 技能要求：Python、TensorFlow、PyTorch、数学基础
    - 薪资范围：20-50万/年
    - 发展前景：★★★★★

    2. 深度学习工程师
    - 职责：开发深度神经网络模型，处理复杂的AI问题
    - 技能要求：深度学习框架、GPU编程、算法优化
    - 薪资范围：25-60万/年
    - 发展前景：★★★★★

    3. 计算机视觉工程师
    - 职责：开发图像识别、目标检测等视觉AI应用
    - 技能要求：OpenCV、图像处理、深度学习
    - 薪资范围：22-45万/年
    - 发展前景：★★★★☆

    4. 自然语言处理工程师
    - 职责：开发文本分析、语音识别等NLP应用
    - 技能要求：NLP库、语言模型、文本挖掘
    - 薪资范围：20-50万/年
    - 发展前景：★★★★★

    5. AI产品经理
    - 职责：规划AI产品功能，协调技术团队开发
    - 技能要求：产品设计、AI技术理解、项目管理
    - 薪资范围：25-55万/年
    - 发展前景：★★★★☆

    三、技能要求分析

    核心技能：
    - 编程语言：Python（必备）、R、Java
    - 机器学习：scikit-learn、XGBoost、LightGBM
    - 深度学习：TensorFlow、PyTorch、Keras
    - 数据处理：Pandas、NumPy、SQL
    - 数学基础：线性代数、概率统计、微积分

    加分技能：
    - 云平台：AWS、Azure、阿里云
    - 大数据：Spark、Hadoop
    - 容器化：Docker、Kubernetes
    - 版本控制：Git
    - 英语能力：阅读英文论文和文档

    四、行业应用领域

    1. 互联网科技：推荐系统、搜索引擎、广告投放
    2. 金融科技：风控模型、量化交易、智能客服
    3. 医疗健康：医学影像分析、药物发现、健康监测
    4. 自动驾驶：感知算法、路径规划、决策系统
    5. 智能制造：质量检测、预测维护、生产优化
    6. 教育科技：个性化学习、智能评测、知识图谱

    五、职业发展路径

    初级工程师 → 高级工程师 → 技术专家/架构师 → 技术总监/CTO
                              ↘ 产品经理 → 产品总监 → CPO

    六、学习建议

    1. 扎实的数学基础：重点学习线性代数、概率统计
    2. 编程能力：熟练掌握Python，了解其他语言
    3. 理论学习：系统学习机器学习和深度学习理论
    4. 实践项目：参与开源项目，积累实战经验
    5. 持续学习：关注最新技术发展，参加相关会议
    6. 软技能：提升沟通能力和团队协作能力

    七、就业前景展望

    AI行业正处于快速发展期，预计未来5-10年内将持续保持高速增长。随着AI技术在各行各业的深入应用，相关岗位需求将持续增加。对于计算机专业的学生来说，AI方向是一个非常有前景的职业选择。

    建议学生在校期间就开始关注AI技术发展，通过课程学习、项目实践、实习经历等方式积累相关经验，为未来的职业发展打下坚实基础。
  `,
  createUserId: 'user_003',
  createTime: Date.now() - 86400000 * 10, // 10天前
  updateUserId: 'user_003',
  updateTime: Date.now() - 86400000 * 2,  // 2天前
  createUserName: '陈教授',
  updateUserName: '陈教授'
});

// 人才培养方案数据
const majorCultivationData = ref({
  records: [
    {
      cultivationId: 'cultivation_001',
      name: '计算机科学与技术专业人才培养方案',
      year: 2024,
      fileName: '2024年计算机科学与技术专业培养方案.pdf',
      fileType: 'PDF',
      fileSize: 2048576, // 2MB
      filePath: '/files/cultivation/2024_cs_cultivation_plan.pdf',
      createUserId: 'user_001',
      createUserName: '张主任',
      createTime: Date.now() - 86400000 * 20,
      updateUserId: 'user_001',
      updateUserName: '张主任',
      updateTime: Date.now() - 86400000 * 5
    },
    {
      cultivationId: 'cultivation_002',
      name: '软件工程方向培养方案',
      year: 2024,
      fileName: '2024年软件工程方向培养方案.docx',
      fileType: 'DOCX',
      fileSize: 1536000, // 1.5MB
      filePath: '/files/cultivation/2024_se_cultivation_plan.docx',
      createUserId: 'user_002',
      createUserName: '李教授',
      createTime: Date.now() - 86400000 * 18,
      updateUserId: 'user_002',
      updateUserName: '李教授',
      updateTime: Date.now() - 86400000 * 3
    },
    {
      cultivationId: 'cultivation_003',
      name: '人工智能方向培养方案',
      year: 2024,
      fileName: '2024年人工智能方向培养方案.pdf',
      fileType: 'PDF',
      fileSize: 2560000, // 2.5MB
      filePath: '/files/cultivation/2024_ai_cultivation_plan.pdf',
      createUserId: 'user_003',
      createUserName: '王教授',
      createTime: Date.now() - 86400000 * 15,
      updateUserId: 'user_003',
      updateUserName: '王教授',
      updateTime: Date.now() - 86400000 * 1
    },
    {
      cultivationId: 'cultivation_004',
      name: '网络安全方向培养方案',
      year: 2023,
      fileName: '2023年网络安全方向培养方案.pdf',
      fileType: 'PDF',
      fileSize: 1843200, // 1.8MB
      filePath: '/files/cultivation/2023_security_cultivation_plan.pdf',
      createUserId: 'user_004',
      createUserName: '赵副教授',
      createTime: Date.now() - 86400000 * 365,
      updateUserId: 'user_004',
      updateUserName: '赵副教授',
      updateTime: Date.now() - 86400000 * 30
    },
    {
      cultivationId: 'cultivation_005',
      name: '大数据技术方向培养方案',
      year: 2023,
      fileName: '2023年大数据技术方向培养方案.docx',
      fileType: 'DOCX',
      fileSize: 1920000, // 1.9MB
      filePath: '/files/cultivation/2023_bigdata_cultivation_plan.docx',
      createUserId: 'user_005',
      createUserName: '刘教授',
      createTime: Date.now() - 86400000 * 300,
      updateUserId: 'user_005',
      updateUserName: '刘教授',
      updateTime: Date.now() - 86400000 * 45
    }
  ],
  total: 5,
  size: 10,
  current: 1,
  pages: 1
});

// 加载状态
const isLoading = ref(false);
const error = ref(null);

// 检查是否有任何内容
const hasAnyContent = computed(() => {
  const data = majorContentData.value;
  return data.introduce || data.info || data.target || data.coursePlan ||
         data.course || data.relation || data.requirement ||
         data.teachingPlan || data.cultivationPlan;
});

// 获取专业内容数据
const fetchMajorContent = async () => {
  try {
    isLoading.value = true;
    error.value = null;

    const response = await getMajorContent();
    console.log('专业内容API响应:', response);

    // 只有当API返回有效数据时才覆盖静态数据
    if (response && response.result && Object.keys(response.result).length > 0) {
      // 检查是否有实际内容，而不是空字符串
      const hasContent = Object.values(response.result).some(value =>
        value && typeof value === 'string' && value.trim() !== ''
      );

      if (hasContent) {
        majorContentData.value = { ...majorContentData.value, ...response.result };
      }
    }
    // 如果API没有数据，保持静态数据不变
  } catch (err) {
    console.error('获取专业内容失败:', err);
    // 不显示错误信息，直接使用静态数据
    console.log('使用静态数据作为fallback');
  } finally {
    isLoading.value = false;
  }
};

// 获取专业调研数据
const fetchMajorInvestigation = async () => {
  try {
    isLoading.value = true;
    error.value = null;

    const response = await getMajorInvestigation();
    console.log('专业调研API响应:', response);

    // 只有当API返回有效数据时才覆盖静态数据
    if (response && response.result && response.result.content && response.result.content.trim() !== '') {
      majorInvestigationData.value = { ...majorInvestigationData.value, ...response.result };
    }
    // 如果API没有数据，保持静态数据不变
  } catch (err) {
    console.error('获取专业调研失败:', err);
    // 不显示错误信息，直接使用静态数据
    console.log('使用静态数据作为fallback');
  } finally {
    isLoading.value = false;
  }
};

// 获取AI岗位分析数据
const fetchMajorAiJob = async () => {
  try {
    isLoading.value = true;
    error.value = null;

    const response = await getMajorAiJob();
    console.log('AI岗位分析API响应:', response);

    // 只有当API返回有效数据时才覆盖静态数据
    if (response && response.result && response.result.content && response.result.content.trim() !== '') {
      majorAiJobData.value = { ...majorAiJobData.value, ...response.result };
    }
    // 如果API没有数据，保持静态数据不变
  } catch (err) {
    console.error('获取AI岗位分析失败:', err);
    // 不显示错误信息，直接使用静态数据
    console.log('使用静态数据作为fallback');
  } finally {
    isLoading.value = false;
  }
};

// 获取人才培养方案列表数据
const fetchMajorCultivationList = async (params = {}) => {
  try {
    isLoading.value = true;
    error.value = null;

    const response = await getMajorCultivationList(params);
    console.log('人才培养方案API响应:', response);

    // 只有当API返回有效数据时才覆盖静态数据
    if (response && response.result && response.result.records && response.result.records.length > 0) {
      majorCultivationData.value = { ...majorCultivationData.value, ...response.result };
    }
    // 如果API没有数据，保持静态数据不变
  } catch (err) {
    console.error('获取人才培养方案失败:', err);
    // 不显示错误信息，直接使用静态数据
    console.log('使用静态数据作为fallback');
  } finally {
    isLoading.value = false;
  }
};

function switchTab(tab) {
  activeTab.value = tab;

  // 根据不同标签获取对应数据
  if (tab.title === '专业内容') {
    fetchMajorContent();
  } else if (tab.title === '专业调研') {
    fetchMajorInvestigation();
  } else if (tab.title === 'AI岗位分析') {
    fetchMajorAiJob();
  } else if (tab.title === '人才培养方案') {
    fetchMajorCultivationList();
  }
}

// 格式化时间
const formatTime = (timestamp) => {
  if (!timestamp) return '暂无';
  const date = new Date(timestamp);
  return date.toLocaleString('zh-CN');
};

// 格式化文件大小
const formatFileSize = (bytes) => {
  if (!bytes || bytes === 0) return '未知';
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(1024));
  return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
};

// 组件挂载时，根据当前标签获取对应数据
onMounted(() => {
  if (activeTab.value.title === '专业内容') {
    fetchMajorContent();
  } else if (activeTab.value.title === '专业调研') {
    fetchMajorInvestigation();
  } else if (activeTab.value.title === 'AI岗位分析') {
    fetchMajorAiJob();
  } else if (activeTab.value.title === '人才培养方案') {
    fetchMajorCultivationList();
  }
});
</script>

<style lang="scss" scoped>
/* 页面整体样式 */
.content-container {
  position: relative;
  padding-top: 10vw;
  padding-left: 3vw;
  padding-right: 3vw;
  
  &::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('@/assets/img/Home/ic_home_bg.png') top/cover no-repeat;
    z-index: -1;
  }
  
  .page-header {
    color: white;
    margin-bottom: 2vw;
    display: flex;
    justify-content: space-between; 
    
    h1 { 
      font-size: 3rem; 
      font-weight: 400;
      margin: 0; 
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3); 
    }
    
    .page-header-desc {
      display: flex;
      margin-top: 5vw;
      gap: 2vw;
      
      .data {
        display: flex;
        flex-direction: column;
        align-items: end;
        gap: 1vw;
        
        div {
          display: flex;
          align-items: flex-end;
          justify-content: center;
          h1 {
            margin: 0; 
            line-height: 1;
            font-size: 3rem; 
          }
          
          p {
            margin: 0 0 0 0.2em;
            line-height: 1; 
            align-self: flex-end; 
            font-size: 1rem; 
          }
        }
      }
    }
  }
}

/* 主内容区样式 */
.main-content {
  display: flex;
  gap: 1.5vw;
  min-height: 70vh;
  background-color: white;
  border-radius: 1vw 1vw 0 0;
  overflow: hidden;
  padding: 2vw;
  margin-top: 10vw;
  
  .nav-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 1vw;
    margin-top: 1.5vw;
    
    h2 {
      font-size: 1.2rem;
      font-weight: 700;
    }
    
    button {
      padding: 0.8vw 2vw;
      border: $course-tabs-solid solid 1px;
      background-color: white;
      cursor: pointer;
      border-radius: 2vw;
      font-size: 1.2rem;
      transition: all 0.3s ease;
      text-align: left;
      
      &:hover {
        background-color: $course-tabs;
        color: $primary-color;
        border: $primary-color solid 1px;
      }
      
      &.active {
        background-color: $course-tabs;
        color: $primary-color;
        border: $primary-color solid 1px;
        font-weight: bold;
      }
    }
  }
  
  .content {
    flex: 4;
    padding: 1vw;
    border-radius: 0.3vw;

    .loading-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 40px;

      .loading-spinner {
        width: 30px;
        height: 30px;
        border: 3px solid #f3f3f3;
        border-top: 3px solid #8a6de3;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin-bottom: 15px;
      }

      p {
        color: #666;
        font-size: 16px;
      }
    }

    .error-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 40px;

      .error-icon {
        font-size: 36px;
        margin-bottom: 15px;
      }

      .error-message {
        color: #e74c3c;
        font-size: 16px;
        margin-bottom: 20px;
        text-align: center;
      }

      .retry-btn {
        padding: 8px 16px;
        background: #8a6de3;
        color: white;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        font-size: 14px;
        transition: background-color 0.3s;

        &:hover {
          background: #7a5dd3;
        }
      }
    }

    .major-content {
      .content-section {
        margin-bottom: 30px;

        h3 {
          color: #333;
          font-size: 18px;
          font-weight: 600;
          margin-bottom: 15px;
          padding-bottom: 8px;
          border-bottom: 2px solid #8a6de3;
        }

        .html-content {
          line-height: 1.6;
          color: #666;

          h1, h2, h3, h4, h5, h6 {
            color: #333;
            margin: 15px 0 10px 0;
          }

          p {
            margin-bottom: 12px;
          }

          ul, ol {
            margin: 10px 0;
            padding-left: 20px;
          }

          li {
            margin-bottom: 5px;
          }
        }
      }

      .empty-content {
        text-align: center;
        padding: 40px;
        color: #999;
        font-size: 16px;
      }
    }

    .major-investigation {
      .content-section {
        margin-bottom: 30px;

        h3 {
          color: #333;
          font-size: 18px;
          font-weight: 600;
          margin-bottom: 15px;
          padding-bottom: 8px;
          border-bottom: 2px solid #8a6de3;
        }

        .investigation-content {
          line-height: 1.6;
          color: #666;

          p {
            margin-bottom: 12px;
            font-size: 16px;
          }
        }
      }

      .meta-info {
        margin-bottom: 30px;

        h3 {
          color: #333;
          font-size: 18px;
          font-weight: 600;
          margin-bottom: 15px;
          padding-bottom: 8px;
          border-bottom: 2px solid #8a6de3;
        }

        .meta-details {
          background: #f8f9fa;
          padding: 20px;
          border-radius: 8px;
          border-left: 4px solid #8a6de3;

          p {
            margin-bottom: 8px;
            color: #666;
            font-size: 14px;

            strong {
              color: #333;
              margin-right: 8px;
            }
          }

          p:last-child {
            margin-bottom: 0;
          }
        }
      }

      .empty-content {
        text-align: center;
        padding: 40px;
        color: #999;
        font-size: 16px;
      }
    }

    .major-cultivation {
      .cultivation-list {
        h3 {
          color: #333;
          font-size: 18px;
          font-weight: 600;
          margin-bottom: 20px;
          padding-bottom: 8px;
          border-bottom: 2px solid #8a6de3;
        }

        .list-container {
          .cultivation-item {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            transition: box-shadow 0.3s ease;

            &:hover {
              box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            }

            .item-header {
              display: flex;
              justify-content: space-between;
              align-items: center;
              margin-bottom: 15px;

              .item-title {
                color: #333;
                font-size: 16px;
                font-weight: 600;
                margin: 0;
              }

              .item-year {
                background: #8a6de3;
                color: white;
                padding: 4px 12px;
                border-radius: 12px;
                font-size: 12px;
                font-weight: 500;
              }
            }

            .item-content {
              display: grid;
              grid-template-columns: 1fr 1fr;
              gap: 20px;
              margin-bottom: 15px;

              .file-info, .meta-info {
                p {
                  margin-bottom: 8px;
                  color: #666;
                  font-size: 14px;

                  strong {
                    color: #333;
                    margin-right: 8px;
                  }
                }

                p:last-child {
                  margin-bottom: 0;
                }
              }
            }

            .item-actions {
              text-align: right;

              .download-btn {
                display: inline-block;
                background: #8a6de3;
                color: white;
                padding: 8px 16px;
                border-radius: 6px;
                text-decoration: none;
                font-size: 14px;
                transition: background-color 0.3s;

                &:hover {
                  background: #7a5dd3;
                }

                i {
                  margin-right: 6px;
                }
              }
            }
          }
        }

        .pagination-info {
          text-align: center;
          padding: 20px;
          background: #f8f9fa;
          border-radius: 8px;
          margin-top: 20px;

          p {
            margin: 0;
            color: #666;
            font-size: 14px;
          }
        }
      }

      .empty-content {
        text-align: center;
        padding: 40px;
        color: #999;
        font-size: 16px;
      }
    }
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>